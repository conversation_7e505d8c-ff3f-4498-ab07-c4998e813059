import { adminAuth, adminDB } from '$lib/server/admin.ts';
import { error, json } from '@sveltejs/kit';
import type { QuestionType } from '$lib/types';
import { FieldValue } from 'firebase-admin/firestore';

export const POST = async ({ request, cookies }) => {

    const { idToken } = await request.json();    

    const expiresIn = 60 * 60 * 24 * 14 * 1000; // 14 days

    const decodedIdToken = await adminAuth.verifyIdToken(idToken);

    if (new Date().getTime() / 1000 - decodedIdToken.auth_time < 5 * 60) {
        const userDoc = await adminDB.collection('users').doc(decodedIdToken.uid).get();

        // * If user is signing in for the first time
        if (!userDoc.exists) {
            // * Create user document
            const userRef = adminDB.collection('users').doc(decodedIdToken.uid);
            await userRef.set({
                email: decodedIdToken.email,
                role: "Pro Trial",
            }, { merge: true });

            const completedQuestionsRef = userRef.collection('completedQuestions').doc('dataDoc');
            // Create stats object mapping all question types to { total: 0, correct: 0 }
            const questionTypes: QuestionType[] = [
                "Word in Context",
                "Main Purpose Underlined",
                "Main Idea",
                "Main Purpose",
                "Overall Structure",
                "Specific Detail",
                "Command of Evidence",
                "Paired Passage",
                "Inference"
            ];
            const stats = Object.fromEntries(
                questionTypes.map(type => [type, { total: 0, correct: 0 }])
            );
            await completedQuestionsRef.set({
                data: [],
                stats,
                markedQuestions: [],
                incorrectlyAnsweredQuestions: []
            });

            const decksRef = userRef.collection("decks");
            await decksRef.add({
                name: "My First Deck",
                description: "Try adding a card to this deck",
                cards: [],
                createdAt: FieldValue.serverTimestamp(),
                updatedAt: FieldValue.serverTimestamp(),
            });
        }

        // Set the role of the user in the custom claims
        const { role } = userDoc.data() || { role: "Free" };
        adminAuth.setCustomUserClaims(decodedIdToken.uid, { role });

        const cookie = await adminAuth.createSessionCookie(idToken, { expiresIn });        

        const options = { maxAge: expiresIn / 1000, httpOnly: true, secure: true, path: '/' };

        cookies.set('__session', cookie, options);

        return json({ status: 'signedIn' });
    } else {
        error(401, 'Recent sign in required!');
    }
};

export const DELETE = async ({ cookies }) => {
    cookies.delete('__session', { path: '/' });
    return json({ status: 'signedOut' });
}