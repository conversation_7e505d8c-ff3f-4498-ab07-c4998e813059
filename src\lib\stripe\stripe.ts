import { DocumentReference, onSnapshot } from "firebase/firestore";

export const handleCheckoutSession = (docRef: DocumentReference, loadingButton?: any) => {
    onSnapshot(docRef, (snap) => {
        const { error, url } = snap.data();
        
        if (error) {
            console.error(error);
            loadingButton?.stopLoading();
            alert("An error occurred. Please try again.");
        }

        if (url) {
            window.location.assign(url);
        }
    });
};