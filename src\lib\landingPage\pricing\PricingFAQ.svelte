<script lang="ts">
    import { H2, P1, P2, AccordionItem } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';

    // State management for accordion items
    let openItems = $state<Set<string>>(new Set());

    function toggleItem(id: string) {
        const newOpenItems = new Set(openItems);
        if (newOpenItems.has(id)) {
            newOpenItems.delete(id);
        } else {
            // Single open behavior - close others
            newOpenItems.clear();
            newOpenItems.add(id);
        }
        openItems = newOpenItems;
    }

    function isOpen(id: string): boolean {
        return openItems.has(id);
    }

</script>

<SectionWrapper --bg-color="var(--white)" --padding-top="4rem" --padding-bottom="4rem">
    <div class="faq-section">
        <div class="faq-header">
            <H2>FAQs</H2>
            <P1>Everything you need to know about our SAT prep platform.</P1>
        </div>
        
        <div class="faq-list">
            <AccordionItem id="faq-1" open={isOpen('faq-1')} toggle={() => toggleItem('faq-1')}>
                {#snippet header()}
                    <span>Is there a 14-day money back guarantee?</span>
                {/snippet}
                {#snippet content()}
                    <P2>Yes, we have a 14-day money back guarantee.</P2>
                    <br>
                    <P2>This means that you can try DSAT16 risk-free for a total of 21 days (7-day free trial + 14-day money back guarantee after your trial ends).</P2>
                    <br>
                    <P2> We know you'll LOVE DSAT16, but if for some reason you decide later that you don't want to be a member anymore, we'll happily cancel your account during that window of time with a full refund.</P2>
                {/snippet}
            </AccordionItem>

            <AccordionItem id="faq-2" open={isOpen('faq-2')} toggle={() => toggleItem('faq-2')}>
                {#snippet header()}
                    <span>Do I need a credit card to sign up for the free trial?</span>
                {/snippet}
                {#snippet content()}
                    <P2>No, you don't need a credit card to sign up for the free trial.</P2>
                    <br>
                    <P2>You can try DSAT16 for free for 7 days. After that, if you decide to upgrade to a paid plan, you will need to provide a credit card.</P2>
                    <br>
                    <P2>However, you can cancel your subscription at any time before your trial ends and you will not be charged.</P2>
                {/snippet}
            </AccordionItem>

            <AccordionItem id="faq-3" open={isOpen('faq-3')} toggle={() => toggleItem('faq-3')}>
                {#snippet header()}
                    <span>Where do the questions come from?</span>
                {/snippet}
                {#snippet content()}
                    <P2>The questions and explanations were written by AI models instructed based on real SAT questions and from my experience studying and teaching SAT.</P2>
                    <br>
                    <P2>The models are researched and tested thoroughly. I can confidently say that they are able to generate questions that are indistinguishable from real SAT questions.</P2>
                    <br>
                    <P2>I know there are still some doubts about AI-generated questions. That's why you can try DSAT16 for free for 7 days and check the questions out yourself. 
                    </P2>
                {/snippet}
            </AccordionItem>
        </div>
    </div>
</SectionWrapper>

<style>
    .faq-header {
        text-align: center;
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .faq-header :global(h2) {
        color: var(--pitch-black);
    }

    .faq-list {
        max-width: 64rem;
        margin: 0 auto;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
</style>
