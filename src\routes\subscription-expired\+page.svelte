<script lang="ts">
    import { page } from "$app/state";
    import NavBar from "$lib/study/NavBar.svelte";
	import Button from "$lib/ui/Button.svelte";
	import { H1, P1 } from "$lib/ui/index.js";
	import P3 from "$lib/ui/typography/P3.svelte";
	import { innerWidth } from "svelte/reactivity/window";

    let { data } = $props();

    let currentPath = $derived(page.url.pathname);
</script>

<svelte:head>
    <title>Subscription Expired | DSAT16</title>
    <meta name="robots" content="noindex">
</svelte:head>

<NavBar {currentPath} role={data.role}/>

<div class="children-container" class:mobile={innerWidth.current < 1024}>
    <H1>Your subscription has expired.</H1>
    <P1>Please renew your subscription to continue using DSAT16.</P1>
    <a href="/pricing"><Button>Renew Subscription</Button></a>
    <P3><a href="/study"><u>Already purchased?</u></a></P3>
</div>

<style>
    .children-container {
        margin: 0 auto;
        margin-left: 4.75rem;
        padding: 4rem;
        text-align: center;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
    }

    .mobile {
        margin-left: 0;
        margin-top: 4rem; /* Account for fixed mobile top navbar */
        padding: 1rem;
    }
</style>

