import { adminAuth } from "$lib/server/admin.ts";
import { redirect, type Handle, type ServerInit } from "@sveltejs/kit";
import { adminDB } from "$lib/server/admin.ts";
import type { HandleServerError } from '@sveltejs/kit';
import { dev } from "$app/environment";
import { phClient } from "$lib/server";
import schedule from 'node-schedule';
import { resetStreakIfNeeded } from "$lib/missions";

// Map to store last update timestamps for each user
const lastUpdateTimestamps = new Map<string, number>();
const DEBOUNCE_TIME = 2000; // 2 seconds debounce time

let unsubscribeListener: (() => void) | null = null;

export const init: ServerInit = async () => {
    // Set up a listener for user role changes in Firestore
    unsubscribeListener = adminDB.collection('users').onSnapshot(
        async (snapshot) => {
            const changes = snapshot.docChanges();
            
            for (const change of changes) {
                if (change.type === 'modified') {
                    const userData = change.doc.data();
                    const userId = change.doc.id;
                    
                    // Check if we should process this update (debouncing)
                    const now = Date.now();
                    const lastUpdate = lastUpdateTimestamps.get(userId) || 0;
                    if (now - lastUpdate < DEBOUNCE_TIME) {
                        console.log(`Skipping rapid update for user ${userId}`);
                        continue;
                    }
                    
                    try {
                        // Get current user to check existing claims
                        const user = await adminAuth.getUser(userId);
                        const currentRole = user.customClaims?.role;
                        
                        // Only update if the role has changed
                        if (currentRole !== userData.role) {                            
                            await adminAuth.setCustomUserClaims(userId, { 
                                role: userData.role 
                            });
                            
                            // Update the timestamp
                            lastUpdateTimestamps.set(userId, now);
                            
                            console.log(`Successfully updated custom claims for user ${userId}: ${currentRole} --> ${userData.role}`);
                        } else {
                            console.log(`No role change needed for user ${userId}, current role: ${currentRole}`);
                        }
                    } catch (error) {
                        console.error(`Error processing role update for user ${userId}:`, error);
                    }
                }
            }
        },
        (error) => {
            console.error('Error in Firestore listener:', error);
        }
    );

    // Cron Job to update subscriptions and streak data
    schedule.scheduleJob('0 0 * * *', async () => {
        try {
            console.log('Running cron job to update subscriptions and streak data...');
            const allUsers = await adminDB.collection('users').get();

            let updatedUsers = 0;

            // Loop through all users
            allUsers.forEach(async (userDoc) => {
                const userData = userDoc.data();
                const userId = userDoc.id;

                // Remove pro trial role after 7 days
                if (userData.role === 'Pro Trial') {
                    const user = await adminAuth.getUser(userId);
                    const dateCreated = user.metadata.creationTime;
                    const daysSinceCreation = Math.floor((Date.now() - Date.parse(dateCreated)) / (1000 * 60 * 60 * 24));
                    if (daysSinceCreation >= 7) {
                        await adminDB.collection("users").doc(userId).update({ role: 'Free' });
                        updatedUsers++;
                    }
                }

                // Update role based on expiration field
                if (userData.role !== "Free") {
                    const expirationDate = userData.expirationDate ? new Date(userData.expirationDate) : null;
                    if (expirationDate && expirationDate < new Date()) {
                        await adminDB.collection("users").doc(userId).update({ role: 'Free' });
                        updatedUsers++;
                    }
                }

                // Reset streak
                await resetStreakIfNeeded(userId);
            });

            console.log(`Cron job completed. Updated roles for ${updatedUsers} users.`);
        } catch (error) {
            console.error('Error in cron job:', error);
        }
    });
};

// Cleanup function to be called when the server shuts down
process.on('SIGTERM', () => {
    if (unsubscribeListener) {
        unsubscribeListener();
        lastUpdateTimestamps.clear();
    }
});

/**
 * Server-side hook that handles authentication and authorization for the application.
 * This hook runs on every request and manages access control for study-related routes.
 */
export const handle: Handle = async ({ event, resolve }) => {
    const sessionCookie = event.cookies.get("__session");
    const isStudyPath = event.url.pathname.startsWith("/study");
    const isSubscriptionExpiredPath = event.url.pathname === "/subscription-expired";
    const isBootcampPath = event.url.pathname.startsWith("/bootcamp");

    // Allow access to all marketing pages, but still populate user info if available
    if (!isStudyPath && !isBootcampPath && !isSubscriptionExpiredPath) {
        if (sessionCookie) {
            try {
                let decodedClaims = await adminAuth.verifySessionCookie(sessionCookie);
                if (decodedClaims) {
                    let user = await adminAuth.getUser(decodedClaims.uid);
                    event.locals.uid = decodedClaims.uid || null;
                    event.locals.role = user.customClaims?.role || null;
                }
            } catch (error) {
            }
        }
        return resolve(event);
    }

    // Require the user to sign up to access the study path or bootcamp path
    if (!sessionCookie) {
        redirectTo(event, "/sign-up");
    }

    let decodedClaims= await adminAuth.verifySessionCookie(sessionCookie);

    if (!decodedClaims) {
        redirectTo(event, "/sign-up");
    }

    let user = await adminAuth.getUser(decodedClaims.uid);

    // Store user information in event.locals for use in the application
    event.locals.uid = decodedClaims.uid || null;
    event.locals.role = user.customClaims.role || null;

    if (isSubscriptionExpiredPath) {
        if (user.customClaims.role === 'Free') return resolve(event);
        redirectTo(event, "/study");
    }

    // If user is Pro or Pro Trial, allow access to all paths
    if (user.customClaims.role === 'Pro' || user.customClaims.role === 'Pro Trial') {
        return resolve(event);
    }

    const hasBootcampAccess = user.customClaims.role === 'Bootcamp';
    const hasStudyAccess = user.customClaims.role === 'Question Bank';

    if (isBootcampPath && !hasBootcampAccess) {
        redirectTo(event, "/subscription-expired");
    }

    if (isStudyPath && !hasStudyAccess) {
        redirectTo(event, "/subscription-expired");
    }    

    return resolve(event);
}

/**
 * Redirects to the sign-up page with the current URL as the redirectTo parameter.
 * This allows users to return to their intended destination after signing up.
 * @param event - The event object containing the current request information
 */
const redirectTo = (event: any, path: string) => {
  const fromUrl = "/" + (event.url.pathname + event.url.search).slice(1);
  redirect(302, `${path}?redirectTo=${fromUrl}`);
};

export const handleError: HandleServerError = async ({ error, status }) => {
    if (status !== 404 && !dev) {
        phClient.captureException(error);
        await phClient.shutdown();
    }

    if (dev) console.error(error);
};
