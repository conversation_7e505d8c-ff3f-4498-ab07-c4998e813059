import { error, json } from '@sveltejs/kit';
import { PayOS } from '@payos/node';
import { PAYOS_API_KEY, PAYOS_CHECKSUM_KEY, PAYOS_CLIENT_ID } from '$env/static/private';
import { dev } from '$app/environment';
import { adminAuth } from '$lib/server/admin';
import { redis } from '$lib/server/redis.js';

const payos = new PayOS({
    clientId: PAYOS_CLIENT_ID,
    apiKey: PAYOS_API_KEY,
    checksumKey: PAYOS_CHECKSUM_KEY

});

export const POST = async ({ request, locals }) => {
    const { amount, isAnnual } = await request.json();

    const uid = locals.uid;
    
    if (!uid) return error(401, "Please sign in to continue.");
    
    const user = await adminAuth.getUser(uid);
    const currentUrl = dev ? 'http://localhost:5173' : 'https://www.dsat16.com';
    
    const paymentObject = await payos.paymentRequests.create({
        // Random 6-digit number
        orderCode: Number(String(Date.now()).slice(-6)),
        buyerEmail: user.email,
        buyerName: user.displayName,
        amount,
        description: `DSAT16 ${isAnnual ? 'Annual' : 'Monthly'} Pro Plan`,
        returnUrl: currentUrl + '/pricing',
        cancelUrl: currentUrl + '/pricing',
        items: [{
            name: `DSAT16 ${isAnnual ? 'Annual' : 'Monthly'} Pro Plan`,
            quantity: 1,
            price: amount
        }],
        // Expires in 30 minutes. These fuckers use 32-bit time for some reason. Will not work 
        expiredAt: Math.floor(Date.now() / 1000) + 60 * 30
    });

    // Save user information to redis. Give some buffer time
    await redis.setex(`payos-payment:${paymentObject.paymentLinkId}`, 1000 * 60 * 32, JSON.stringify({
        uid,
        isAnnual
    }));

    return json(paymentObject.checkoutUrl);
}