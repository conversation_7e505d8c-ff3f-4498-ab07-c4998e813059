import { getContext, setContext } from "svelte";

const ANNUAL_CONTEXT = Symbol("isAnnual");
const VIETNAMESE_CONTEXT = Symbol("isInVietnam");
const OPEN_CONTEXT = Symbol("isOpen");

export function setPricingContext(getIsAnnual: () => boolean, getIsInVietnam: () => boolean, changeIsOpen: () => void) {

    return {
        getIsAnnual: setContext(ANNUAL_CONTEXT, getIsAnnual),
        getIsInVietnam: setContext(VIETNAMESE_CONTEXT, getIsInVietnam),
        changeIsOpen: setContext(OPEN_CONTEXT, changeIsOpen),
    }
}

export function getPricingContext() {
    return {
        getIsAnnual: getContext<() => boolean>(ANNUAL_CONTEXT),
        getIsInVietnam: getContext<() => boolean>(VIETNAMESE_CONTEXT),
        changeIsOpen: getContext<() => void>(OPEN_CONTEXT),
    } as ReturnType<typeof setPricingContext>;
}