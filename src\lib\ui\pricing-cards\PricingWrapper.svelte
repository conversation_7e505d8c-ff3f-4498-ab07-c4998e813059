<!-- 
    @component
    A wrapper component for pricing cards. Required.
    
    Usage:
    ```svelte
    <PricingWrapper {pricingContext}>
        <PricingCard />
    </PricingWrapper>
    ```
-->

<script lang="ts">
	import type { Snippet } from "svelte";
    import { setPricingContext } from "./pricingContext.svelte.ts";
	import PopUp from "../PopUp.svelte";

    interface Props {
        pricingContext: {
            isAnnual: boolean;
            isInVietnam: boolean;
        },
        children: Snippet;
    }

    let { pricingContext, children }: Props = $props();
    let isOpen = $state(false);

    setPricingContext(() => pricingContext.isAnnual, () => pricingContext.isInVietnam, () => isOpen = !isOpen);
</script>

<PopUp bind:isOpen={isOpen} size="medium" closeOnOutsideClick={false} closeOnEscape={false}>
    <div id="payos-pricing"></div>
</PopUp>

{@render children?.()}

<style>
    #payos-pricing {
        min-height: 24rem;

        & > :global(iframe) {
            min-height: 24rem;
            min-width: 22rem;

            &:global(.bg-white) {
                height: 100% !important;
            }
        }
    }
</style>