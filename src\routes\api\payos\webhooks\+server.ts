import { adminDB } from '$lib/server/admin.js';
import { payos } from '$lib/server/payos.ts';
import { redis } from '$lib/server/redis.js';
import type { Webhook } from '@payos/node';
import { error, json } from '@sveltejs/kit';

export const POST = async ({ request }) => {
    const data: Webhook = await request.json();

    if (data.data.code === '00') {

        try {
            const webhookData = await payos.webhooks.verify(data);
            
            // Fetch data from redis
            const paymentData: { uid: string, isAnnual: boolean } = await redis.get(`payos-payment:${webhookData.paymentLinkId}`);

            if (!paymentData) return error(400, 'Bad Request');

            const { uid, isAnnual } = paymentData;

            // Update on firebase
            await adminDB.collection('users').doc(uid).update({
                role: 'Pro',
                expirationDate: Date.now() + (isAnnual ? 365 : 30) * 24 * 60 * 60 * 1000
            });

            await redis.del(`payos-payment:${webhookData.paymentLinkId}`);
            console.log(`Payment successful. User ${uid} upgraded to Pro.`);

            return json('success');

        } catch (error) {
            console.error(error);
            return error(400, 'Bad Request');
        }
    }

    return json('success');
}