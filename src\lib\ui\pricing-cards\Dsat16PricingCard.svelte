<script lang="ts">
    import PricingCard from '$lib/ui/pricing-cards/PricingCard.svelte';
    
    interface Plan {
        label: string;
        description: string;
        monthlyPrice: string;
        monthlyPriceIfPaidYearly: string;
        vietnameseMonthlyPrice: string;
        vietnameseMonthlyPriceIfPaidYearly: string;
        checklist: { text: string; subtext?: string }[];
    }

    let plan = $state<Plan>({
        label: "Pro Plan", 
        description: "Get unlimited access to all features. Study at your own pace.",
        monthlyPrice: "$27/mo",
        monthlyPriceIfPaidYearly: "$16/mo", 
        vietnameseMonthlyPrice: "687,000đ/mo",
        vietnameseMonthlyPriceIfPaidYearly: "387,000đ/mo",
        checklist: [
            { text: "Weekly added mock tests" },
            { text: "Detailed performance analytics" },
            { text: "5000+ practice questions" },
            { text: "Time-optimized Vocab Learning Tool" },
            { text: "Gamified Learning Experience to keep you motivated" },
            { text: "7-day free trial" },
            { text: "14-day money-back guarantee" }
        ]
    })
</script>

<PricingCard
    --box-shadow-color="var(--pitch-black)"
    --svg-color="var(--aquamarine)"
    --label-bg-color="var(--light-sky-blue)"
    {...plan}
    monthlyPriceId="price_1SAZoMJPIuRILQq6bjpGYXhh"
    yearlyPriceId="price_1SAZp3JPIuRILQq6JGIXa4uA"
/>